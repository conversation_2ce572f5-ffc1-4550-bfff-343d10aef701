import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Stack } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Dimensions,
    Platform,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

const { width } = Dimensions.get('window');
const SIDEBAR_WIDTH = 280;
const SIDEBAR_COLLAPSED_WIDTH = 70;

interface Function {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'crud' | 'analytics' | 'reports' | 'admin';
}

interface Category {
  id: string;
  title: string;
  icon: string;
  color: string;
  functions: Function[];
}

const categories: Category[] = [
  {
    id: 'crud',
    title: 'მონაცემების მართვა',
    icon: 'square.grid.3x3.fill',
    color: '#4CAF50',
    functions: [
      {
        id: 'users-crud',
        title: 'მომხმარებლების მართვა',
        description: 'მომხმარებლების დამატება, რედაქტირება, წაშლა',
        icon: 'person.3.fill',
        category: 'crud',
      },
      {
        id: 'products-crud',
        title: 'პროდუქტების მართვა',
        description: 'პროდუქტების კატალოგის მართვა',
        icon: 'cube.box.fill',
        category: 'crud',
      },
      {
        id: 'orders-crud',
        title: 'შეკვეთების მართვა',
        description: 'შეკვეთების ნახვა და მართვა',
        icon: 'cart.fill',
        category: 'crud',
      },
    ],
  },
  {
    id: 'analytics',
    title: 'ანალიტიკა',
    icon: 'chart.line.uptrend.xyaxis',
    color: '#2196F3',
    functions: [
      {
        id: 'sales-analytics',
        title: 'გაყიდვების ანალიზი',
        description: 'გაყიდვების სტატისტიკა და ანალიზი',
        icon: 'chart.line.uptrend.xyaxis',
        category: 'analytics',
      },
      {
        id: 'user-analytics',
        title: 'მომხმარებლების ანალიზი',
        description: 'მომხმარებლების აქტივობის ანალიზი',
        icon: 'person.crop.circle.badge.checkmark',
        category: 'analytics',
      },
    ],
  },
  {
    id: 'reports',
    title: 'რეპორტები',
    icon: 'doc.text.fill',
    color: '#FF9800',
    functions: [
      {
        id: 'financial-reports',
        title: 'ფინანსური რეპორტები',
        description: 'შემოსავლების და ხარჯების რეპორტები',
        icon: 'dollarsign.circle.fill',
        category: 'reports',
      },
    ],
  },
];

export default function MainLayout() {
  const { user, logout, isLoading } = useAuth();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [selectedFunction, setSelectedFunction] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['crud']);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Redirect to login if not authenticated
  if (!user && !isLoading) {
    return null; // This will trigger the root layout to show login
  }

  const handleLogout = () => {
    Alert.alert(
      'გასვლა',
      'დარწმუნებული ხართ, რომ გსურთ გასვლა?',
      [
        { text: 'გაუქმება', style: 'cancel' },
        { text: 'გასვლა', style: 'destructive', onPress: logout },
      ]
    );
  };

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(prev => !prev);
    // When collapsing, close all categories
    if (!sidebarCollapsed) {
      setExpandedCategories([]);
    } else {
      // When expanding, open the first category
      setExpandedCategories(['crud']);
    }
  };

  const handleFunctionSelect = (func: Function) => {
    setSelectedFunction(func.id);
    // TODO: Navigate to function page
    Alert.alert(func.title, `${func.description}\n\nეს ფუნქცია მალე იქნება ხელმისაწვდომი.`);
  };

  return (
    <View style={styles.container}>
      {/* Sidebar */}
      <View style={[
        styles.sidebar,
        {
          backgroundColor: colors.card,
          borderRightColor: colors.border,
          width: sidebarCollapsed ? SIDEBAR_COLLAPSED_WIDTH : SIDEBAR_WIDTH
        }
      ]}>
        {/* Header */}
        <View style={[styles.sidebarHeader, { backgroundColor: colors.tint }]}>
          {!sidebarCollapsed && (
            <View style={styles.userInfo}>
              <ThemedText style={styles.welcomeText}>მოგესალმებით</ThemedText>
              <ThemedText style={styles.userName}>{user?.name}</ThemedText>
            </View>
          )}
          <View style={styles.headerButtons}>
            <TouchableOpacity onPress={toggleSidebar} style={styles.toggleButton}>
              <IconSymbol
                name={sidebarCollapsed ? "sidebar.right" : "sidebar.left"}
                size={20}
                color="white"
              />
            </TouchableOpacity>
            {!sidebarCollapsed ? (
              <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
                <IconSymbol name="rectangle.portrait.and.arrow.right" size={20} color="white" />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
                <IconSymbol name="rectangle.portrait.and.arrow.right" size={16} color="white" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Navigation */}
        <ScrollView style={styles.navigation} showsVerticalScrollIndicator={false}>
          {!sidebarCollapsed && (
            <ThemedText style={[styles.navigationTitle, { color: colors.text }]}>
              ფუნქციები
            </ThemedText>
          )}

          {categories.map((category) => (
            <View key={category.id} style={styles.categoryContainer}>
              <TouchableOpacity
                style={[
                  styles.categoryHeader,
                  { backgroundColor: colors.backgroundSecondary },
                  sidebarCollapsed && styles.categoryHeaderCollapsed
                ]}
                onPress={() => sidebarCollapsed ? null : toggleCategory(category.id)}
              >
                <View style={[styles.categoryHeaderLeft, sidebarCollapsed && styles.categoryHeaderLeftCollapsed]}>
                  <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                    <IconSymbol name={category.icon} size={16} color="white" />
                  </View>
                  {!sidebarCollapsed && (
                    <ThemedText style={[styles.categoryTitle, { color: colors.text }]}>
                      {category.title}
                    </ThemedText>
                  )}
                </View>
                {!sidebarCollapsed && (
                  <IconSymbol
                    name={expandedCategories.includes(category.id) ? "chevron.down" : "chevron.right"}
                    size={14}
                    color={colors.text}
                  />
                )}
              </TouchableOpacity>

              {!sidebarCollapsed && expandedCategories.includes(category.id) && (
                <View style={styles.functionsContainer}>
                  {category.functions.map((func) => (
                    <TouchableOpacity
                      key={func.id}
                      style={[
                        styles.functionItem,
                        selectedFunction === func.id && {
                          backgroundColor: colors.tint + '20',
                          borderLeftColor: colors.tint,
                          borderLeftWidth: 3,
                        }
                      ]}
                      onPress={() => handleFunctionSelect(func)}
                    >
                      <IconSymbol name={func.icon} size={16} color={category.color} />
                      <View style={styles.functionTextContainer}>
                        <ThemedText style={[styles.functionTitle, { color: colors.text }]}>
                          {func.title}
                        </ThemedText>
                        <ThemedText style={[styles.functionDescription, { color: colors.text + '80' }]}>
                          {func.description}
                        </ThemedText>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          ))}
        </ScrollView>
      </View>

      {/* Main Content */}
      <View style={styles.mainContent}>
        <Stack>
          <Stack.Screen name="index" options={{ headerShown: false }} />
        </Stack>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
  },
  sidebar: {
    borderRightWidth: 1,
  },
  sidebarHeader: {
    padding: 20,
    paddingTop: Platform.OS === 'web' ? 20 : 50,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
  },
  welcomeText: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
  },
  userName: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  toggleButton: {
    padding: 8,
  },
  logoutButton: {
    padding: 8,
  },
  navigation: {
    flex: 1,
    padding: 16,
  },
  navigationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  categoryContainer: {
    marginBottom: 8,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  categoryHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryHeaderCollapsed: {
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  categoryHeaderLeftCollapsed: {
    justifyContent: 'center',
    flex: 0,
  },
  categoryIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  functionsContainer: {
    marginLeft: 16,
    marginTop: 4,
  },
  functionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    borderRadius: 6,
    marginBottom: 4,
  },
  functionTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  functionTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  functionDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  mainContent: {
    flex: 1,
  },
});
