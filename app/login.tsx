import { ThemedText } from '@/components/ThemedText';
import { useToast } from '@/components/Toast';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

export default function LoginScreen() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { toast, showToast, hideToast } = useToast();

  const handleLogin = async () => {
    if (!username.trim() || !password.trim()) {
      showToast('გთხოვთ შეავსოთ ყველა ველი', 'warning');
      return;
    }

    setIsLoading(true);
    const success = await login(username, password, rememberMe);
    setIsLoading(false);

    if (success) {
      showToast('წარმატებით შეხვედით!', 'success');
      setTimeout(() => {
        router.replace('/(main)');
      }, 1000);
    } else {
      showToast('არასწორი მომხმარებლის სახელი ან პაროლი', 'error');
    }
  };

  return (
    <View style={styles.container}>
      {/* Background */}
      <View style={[styles.background, { backgroundColor: colors.background }]}>
        <View style={[styles.backgroundPattern, { backgroundColor: colors.tint + '10' }]} />
        <View style={[styles.backgroundCircle1, { backgroundColor: colors.tint + '20' }]} />
        <View style={[styles.backgroundCircle2, { backgroundColor: colors.accent + '15' }]} />
      </View>

      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={[styles.formContainer, { backgroundColor: colors.card }]}>
            <ThemedText type="title" style={styles.title}>
              ავტორიზაცია
            </ThemedText>
            <ThemedText style={[styles.subtitle, { color: colors.icon }]}>
              შეიყვანეთ თქვენი მონაცემები
            </ThemedText>

            <View style={styles.inputContainer}>
              <ThemedText style={styles.label}>მომხმარებლის სახელი</ThemedText>
              <TextInput
                style={[styles.input, {
                  borderColor: colors.border,
                  backgroundColor: colors.backgroundSecondary,
                  color: colors.text
                }]}
                value={username}
                onChangeText={setUsername}
                placeholder="შეიყვანეთ მომხმარებლის სახელი"
                placeholderTextColor={colors.tabIconDefault}
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputContainer}>
              <ThemedText style={styles.label}>პაროლი</ThemedText>
              <TextInput
                style={[styles.input, {
                  borderColor: colors.border,
                  backgroundColor: colors.backgroundSecondary,
                  color: colors.text
                }]}
                value={password}
                onChangeText={setPassword}
                placeholder="შეიყვანეთ პაროლი"
                placeholderTextColor={colors.tabIconDefault}
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => setRememberMe(!rememberMe)}
            >
              <View style={[
                styles.checkbox,
                { borderColor: colors.border },
                rememberMe && { backgroundColor: colors.tint }
              ]}>
                {rememberMe && <Text style={styles.checkmark}>✓</Text>}
              </View>
              <ThemedText style={styles.checkboxLabel}>დამახსოვრება</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.loginButton, { backgroundColor: colors.tint }]}
              onPress={handleLogin}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.loginButtonText}>შესვლა</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Toast Notification */}
      <Toast
        message={toast.message}
        type={toast.type}
        visible={toast.visible}
        onHide={hideToast}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backgroundPattern: {
    position: 'absolute',
    top: -100,
    right: -100,
    width: 300,
    height: 300,
    borderRadius: 150,
    opacity: 0.3,
  },
  backgroundCircle1: {
    position: 'absolute',
    top: 100,
    left: -50,
    width: 200,
    height: 200,
    borderRadius: 100,
    opacity: 0.2,
  },
  backgroundCircle2: {
    position: 'absolute',
    bottom: -50,
    right: -50,
    width: 250,
    height: 250,
    borderRadius: 125,
    opacity: 0.15,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  formContainer: {
    padding: 32,
    borderRadius: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 10,
    marginHorizontal: 4,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
    fontSize: 32,
    fontWeight: 'bold',
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 32,
    fontSize: 16,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    marginBottom: 8,
    fontSize: 16,
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderRadius: 16,
    padding: 16,
    fontSize: 16,
    minHeight: 56,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 32,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderRadius: 6,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: 16,
  },
  loginButton: {
    padding: 18,
    borderRadius: 16,
    alignItems: 'center',
    minHeight: 56,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
