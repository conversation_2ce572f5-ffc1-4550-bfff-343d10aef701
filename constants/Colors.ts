/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#6366f1';
const tintColorDark = '#8b5cf6';

export const Colors = {
  light: {
    text: '#1f2937',
    background: '#ffffff',
    backgroundSecondary: '#f8fafc',
    card: '#ffffff',
    border: '#e5e7eb',
    tint: tintColorLight,
    icon: '#6b7280',
    tabIconDefault: '#9ca3af',
    tabIconSelected: tintColorLight,
    surface: '#f1f5f9',
    accent: '#3b82f6',
  },
  dark: {
    text: '#f9fafb',
    background: '#0f172a',
    backgroundSecondary: '#1e293b',
    card: '#1e293b',
    border: '#334155',
    tint: tintColorDark,
    icon: '#94a3b8',
    tabIconDefault: '#64748b',
    tabIconSelected: tintColorDark,
    surface: '#334155',
    accent: '#6366f1',
  },
};
